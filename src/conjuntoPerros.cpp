#include "../include/conjuntoPerros.h"

struct rep_conjuntoperros{
    bool* elementos;  // Arreglo de booleanos para indicar presencia
    int cantMax;      // Cantidad máxima de elementos
    int cardinal;     // Cantidad actual de elementos
};

/* Debe ejectuar en O(n) peor caso, siendo n la cantidad máxima de elementos del conjunto. */
TConjuntoPerros crearTConjuntoPerros(int cantMax){
    TConjuntoPerros nuevoConj = new rep_conjuntoperros;
    nuevoConj->cantMax = cantMax;
    nuevoConj->cardinal = 0;
    nuevoConj->elementos = new bool[cantMax];

    // Inicializar todos los elementos en false
    for (int i = 0; i < cantMax; i++) {
        nuevoConj->elementos[i] = false;
    }

    return nuevoConj;
}

/* Debe ejecutar en O(1) peor caso. */
void insertarTConjuntoPerros(TConjuntoPerros &c, int id){
    // Verificar que id esté en el rango válido y no esté ya presente
    if (id >= 0 && id < c->cantMax && !c->elementos[id]) {
        c->elementos[id] = true;
        c->cardinal++;
    }
}

/* Debe ejecutar en O(n) peor caso, siendo "n" la cantidad máxima de elementos de "c". */
void imprimirTConjuntoPerros(TConjuntoPerros c){
    bool primero = true;
    for (int i = 0; i < c->cantMax; i++) {
        if (c->elementos[i]) {
            if (!primero) {
                printf(" ");
            }
            printf("%d", i);
            primero = false;
        }
    }
    printf("\n");
}

/* Debe ejecutar en O(1) peor caso. */
// Luego de la función, el puntero c debe apuntar a NULL.
void liberarTConjuntoPerros(TConjuntoPerros &c){
    if (c != NULL) {
        delete[] c->elementos;
        delete c;
        c = NULL;
    }
}

/* Debe ejecutar en O(1) peor caso. */
bool esVacioTConjuntoPerros(TConjuntoPerros c){
    if (c == NULL) return true;
    return c->cardinal == 0;
}

/* Debe ejecutar en O(1) peor caso. */
int cardinalTConjuntoPerros(TConjuntoPerros c){
    if (c == NULL) return 0;
    return c->cardinal;
}

/* Debe ejecutar en O(1) peor caso. */
int cantMaxTConjuntoPerros(TConjuntoPerros c){
    if (c == NULL) return 0;
    return c->cantMax;
}

/* Debe ejecutar en O(1) peor caso. */
bool perteneceTConjuntoPerros(TConjuntoPerros c, int id){
    if (c == NULL || id < 0 || id >= c->cantMax) return false;
    return c->elementos[id];
}

/* Debe ejecutar en O(1) peor caso. */
void borrarDeTConjuntoPerros(TConjuntoPerros &c, int id){
    // Verificar que id esté en el rango válido y esté presente
    if (id >= 0 && id < c->cantMax && c->elementos[id]) {
        c->elementos[id] = false;
        c->cardinal--;
    }
}

/* Debe ejecutar en O(n) peor caso, siendo n la cantidad máxima de elementos permitidos en el conjunto */
TConjuntoPerros unionTConjuntoPerros(TConjuntoPerros c1, TConjuntoPerros c2){
    TConjuntoPerros resultado = crearTConjuntoPerros(c1->cantMax);

    for (int i = 0; i < c1->cantMax; i++) {
        if (c1->elementos[i] || c2->elementos[i]) {
            resultado->elementos[i] = true;
            resultado->cardinal++;
        }
    }

    return resultado;
}

/* Debe ejecutar en O(n) peor caso, siendo n la cantidad máxima de elementos permitidos en el conjunto */
TConjuntoPerros interseccionTConjuntoPerros(TConjuntoPerros c1, TConjuntoPerros c2){
    TConjuntoPerros resultado = crearTConjuntoPerros(c1->cantMax);

    for (int i = 0; i < c1->cantMax; i++) {
        if (c1->elementos[i] && c2->elementos[i]) {
            resultado->elementos[i] = true;
            resultado->cardinal++;
        }
    }

    return resultado;
}

/* Debe ejecutar en O(n) peor caso, siendo n la cantidad máxima de elementos permitidos en el conjunto */
TConjuntoPerros diferenciaTConjuntoPerros(TConjuntoPerros c1, TConjuntoPerros c2){
    TConjuntoPerros resultado = crearTConjuntoPerros(c1->cantMax);

    for (int i = 0; i < c1->cantMax; i++) {
        if (c1->elementos[i] && !c2->elementos[i]) {
            resultado->elementos[i] = true;
            resultado->cardinal++;
        }
    }

    return resultado;
}
