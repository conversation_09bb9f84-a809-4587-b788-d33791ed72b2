#include "../include/conjuntoPerros.h"

struct rep_conjuntoperros{

};

/* Debe ejectuar en O(n) peor caso, siendo n la cantidad máxima de elementos del conjunto. */
TConjuntoPerros crearTConjuntoPerros(int cantMax){
  return NULL;
}

/* Debe ejecutar en O(1) peor caso. */
void insertarTConjuntoPerros(TConjuntoPerros &c, int id){

}

/* Debe ejecutar en O(n) peor caso, siendo "n" la cantidad máxima de elementos de "c". */
void imprimirTConjuntoPerros(TConjuntoPerros c){

}

/* Debe ejecutar en O(1) peor caso. */
// Luego de la función, el puntero c debe apuntar a NULL.
void liberarTConjuntoPerros(TConjuntoPerros &c){

}

/* Debe ejecutar en O(1) peor caso. */
bool esVacioTConjuntoPerros(TConjuntoPerros c){
  return true;
}

/* Debe ejecutar en O(1) peor caso. */
int cardinalTConjuntoPerros(TConjuntoPerros c){
  return 0;
}

/* Debe ejecutar en O(1) peor caso. */
int cantMaxTConjuntoPerros(TConjuntoPerros c){
  return 0;
}

/* Debe ejecutar en O(1) peor caso. */
bool perteneceTConjuntoPerros(TConjuntoPerros c, int id){
  return false;
}

/* Debe ejecutar en O(1) peor caso. */
void borrarDeTConjuntoPerros(TConjuntoPerros &c, int id){

}

/* Debe ejecutar en O(n) peor caso, siendo n la cantidad máxima de elementos permitidos en el conjunto */
TConjuntoPerros unionTConjuntoPerros(TConjuntoPerros c1, TConjuntoPerros c2){
  return NULL;
}

/* Debe ejecutar en O(n) peor caso, siendo n la cantidad máxima de elementos permitidos en el conjunto */
TConjuntoPerros interseccionTConjuntoPerros(TConjuntoPerros c1, TConjuntoPerros c2){
  return NULL;
}

/* Debe ejecutar en O(n) peor caso, siendo n la cantidad máxima de elementos permitidos en el conjunto */
TConjuntoPerros diferenciaTConjuntoPerros(TConjuntoPerros c1, TConjuntoPerros c2){
  return NULL;
}
