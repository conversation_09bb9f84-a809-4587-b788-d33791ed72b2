#include "../include/ldePerros.h"

struct nodo_perro{
    nodo_perro* sig; // Puntero al siguiente nodo.
    nodo_perro* ant; // Puntero al nodo anterior.
    TPerro perro; // Almacenar datos de tipo TPerro.
};

struct rep_tldeperros { //Cabezal
    nodo_perro* inicio; // Puntero al inicio de la lista.
    nodo_perro* final; // Puntero al final de la lista.
    int perrosenlista; // Control de perros en lista.
};


TLDEPerros crearTLDEPerrosVacia(){
    TLDEPerros nuevaLista = new rep_tldeperros;
    nuevaLista->inicio = NULL;
    nuevaLista->final = NULL;
    nuevaLista->perrosenlista = 0;
    return nuevaLista;
}

void insertarTLDEPerros(TLDEPerros &ldePerros, TPerro perro){
    nodo_perro *nuevoperro = new nodo_perro;
    nuevoperro->perro = perro;
    nuevoperro->ant = NULL;
    nuevoperro->sig = NULL;
    if (ldePerros->inicio == NULL){
        ldePerros->inicio = nuevoperro;
        ldePerros->final = nuevoperro;
    } else if (edadTPerro(nuevoperro->perro) <= edadTPerro(ldePerros->inicio->perro)) { // Si la fecha es menor a la fecha del primer elemento de la lista, se agrega al comienzo de la lista.
        ldePerros->inicio->ant = nuevoperro;
        nuevoperro->sig = ldePerros->inicio;
        ldePerros->inicio = nuevoperro;
    } else {
        nodo_perro* aux;
        aux = ldePerros->inicio ; // Puntero auxiliar para recorrer y comparar fechas en la lista, partiendo del segundo nodo.
        while ((aux->sig != NULL) && (edadTPerro(perro) > edadTPerro(aux->sig->perro))) {
            aux = aux->sig;
        };
        if (aux->sig == NULL){ // Se agrega al final de la lista.
            nuevoperro->ant = ldePerros->final;
            ldePerros->final->sig = nuevoperro;
            ldePerros->final = nuevoperro;
        } else {
            nuevoperro->sig = aux->sig; // Se agrega en el medio de la lista.
            aux->sig->ant = nuevoperro;
            nuevoperro->ant = aux;
            aux->sig = nuevoperro;
        }
    }
    ldePerros->perrosenlista++;
}

void liberarTLDEPerros(TLDEPerros &ldePerros){
    nodo_perro* actual = ldePerros->inicio;
    while (actual != NULL){
        nodo_perro* siguiente = actual->sig;
        liberarTPerro(actual->perro); // Libera memoria del perro.
        delete actual; // Libera memoria del nodo actual.
        actual = siguiente;
    }
    delete ldePerros;
    ldePerros = NULL;
}

void imprimirTLDEPerros(TLDEPerros ldePerros){
    nodo_perro* actual = ldePerros->inicio;
    printf("LDE Perros:\n");
    while (actual != NULL){
        imprimirTPerro(actual->perro);
        actual = actual->sig;
    }
}

void imprimirInvertidoTLDEPerros(TLDEPerros ldePerros){
    nodo_perro* actual = ldePerros->final;
    printf("LDE Perros:\n");
    while (actual != NULL){
        imprimirTPerro(actual->perro);
        actual = actual->ant;
    }
}

nat cantidadTLDEPerros(TLDEPerros ldePerros){
    if (ldePerros == NULL){
        return 0;
    }
    return ldePerros->perrosenlista;
}

TPerro removerPerroTLDEPerros(TLDEPerros &ldePerros, int id){
    nodo_perro* actual = ldePerros->inicio;
;
    while (idTPerro(actual->perro) != id){
        actual = actual->sig;
    }
    TPerro perroeliminado = actual->perro;
    if (actual->ant != NULL){
        actual->ant->sig = actual->sig;
    } else {
        ldePerros->inicio = actual->sig;
    }
    if (actual->sig != NULL){
        actual->sig->ant = actual->ant;
    } else {
        ldePerros->final = actual->ant;
    }
    delete actual;
    ldePerros->perrosenlista--;
    return perroeliminado;
}

TPerro obtenerPrimeroTLDEPerros(TLDEPerros ldePerros){
    return ldePerros->inicio->perro;
}

TPerro obtenerUltimoTLDEPerros(TLDEPerros ldePerros){
    return ldePerros->final->perro;
}

TPerro obtenerNesimoTLDEPerros(TLDEPerros ldePerros, int n){
    if (ldePerros == NULL || ldePerros->inicio == NULL) { // Verifica si la lista está vacía.
        return NULL;
    }
    if (n <= 0 || n > ldePerros->perrosenlista) { // Verifica si n es válido.
        return NULL;
    }
    nodo_perro* actual = ldePerros->inicio;
    for (int i = 1; i < n; i++){
        actual = actual->sig;
    }
    return actual->perro;
}

bool existePerroTLDEPerros(TLDEPerros ldePerros, int id){
    nodo_perro* actual = ldePerros->inicio;
    while (actual != NULL){
        if (idTPerro(actual->perro) == id){
            return true;
        }
        actual = actual->sig;
    }
    return false;
}

/* ********** FUNCIONES NUEVAS TAREA 3 ********** */

TPerro removerPrimeroTLDEPerros(TLDEPerros &ldePerros) {
    // PRE: cantidadTLDEPerros(ldePerros) > 0
    nodo_perro* primero = ldePerros->inicio;
    TPerro perroRemovido = primero->perro;

    // Si solo hay un elemento
    if (ldePerros->inicio == ldePerros->final) {
        ldePerros->inicio = NULL;
        ldePerros->final = NULL;
    } else {
        // Hay más de un elemento
        ldePerros->inicio = primero->sig;
        ldePerros->inicio->ant = NULL;
    }

    delete primero;
    ldePerros->perrosenlista--;
    return perroRemovido;
}

TPerro removerUltimoTLDEPerros(TLDEPerros &ldePerros) {
    // PRE: cantidadTLDEPerros(ldePerros) > 0
    nodo_perro* ultimo = ldePerros->final;
    TPerro perroRemovido = ultimo->perro;

    // Si solo hay un elemento
    if (ldePerros->inicio == ldePerros->final) {
        ldePerros->inicio = NULL;
        ldePerros->final = NULL;
    } else {
        // Hay más de un elemento
        ldePerros->final = ultimo->ant;
        ldePerros->final->sig = NULL;
    }

    delete ultimo;
    ldePerros->perrosenlista--;
    return perroRemovido;
}

