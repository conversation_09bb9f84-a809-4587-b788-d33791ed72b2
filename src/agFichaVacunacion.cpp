
#include "../include/agFichaVacunacion.h"

struct nodo_ag {
    int codVacuna;
    nodo_ag* primerHijo;
    nodo_ag* sigHermano;
};

struct rep_agFichaVacunacion {
    nodo_ag* raiz;
};

// Función auxiliar para buscar un nodo con un código específico
nodo_ag* buscarNodo(nodo_ag* nodo, int codVacuna) {
    if (nodo == NULL) return NULL;

    if (nodo->codVacuna == codVacuna) return nodo;

    // Buscar en los hijos
    nodo_ag* resultado = buscarNodo(nodo->primerHijo, codVacuna);
    if (resultado != NULL) return resultado;

    // Buscar en los hermanos
    return buscarNodo(nodo->sigHermano, codVacuna);
}

/* Debe ejecutar en O(1) peor caso. */
TAGFichaVacunacion crearTAGFichaVacunacion(){
    TAGFichaVacunacion nuevaFicha = new rep_agFichaVacunacion;
    nuevaFicha->raiz = NULL;
    return nuevaFicha;
}

/* Debe ejecutar en O(n) peor caso, siendo n la cantidad de vacunas en el árbol. */
void insertarVacunaTAGFichaVacunacion(TAGFichaVacunacion &fichaVacunacion, int codVacunaPadre, int codVacuna){
    nodo_ag* nuevoNodo = new nodo_ag;
    nuevoNodo->codVacuna = codVacuna;
    nuevoNodo->primerHijo = NULL;
    nuevoNodo->sigHermano = NULL;

    if (codVacunaPadre == -1) {
        // Insertar como raíz
        fichaVacunacion->raiz = nuevoNodo;
    } else {
        // Buscar el nodo padre
        nodo_ag* padre = buscarNodo(fichaVacunacion->raiz, codVacunaPadre);

        // Insertar como primer hijo (los hermanos se insertan en orden inverso)
        nuevoNodo->sigHermano = padre->primerHijo;
        padre->primerHijo = nuevoNodo;
    }
}

// Función auxiliar para imprimir con indentación
void imprimirNodoConIndentacion(nodo_ag* nodo, int nivel) {
    if (nodo == NULL) return;

    // Imprimir indentación (4 espacios por nivel)
    for (int i = 0; i < nivel; i++) {
        printf("    ");
    }
    printf("%d\n", nodo->codVacuna);

    // Imprimir hijos
    imprimirNodoConIndentacion(nodo->primerHijo, nivel + 1);

    // Imprimir hermanos
    imprimirNodoConIndentacion(nodo->sigHermano, nivel);
}

/*Debe ejecutar en O(n) peor caso, donde n es la cantidad de vacunas en el árbol. */
void imprimirTAGFichaVacunacion(TAGFichaVacunacion fichaVacunacion){
    printf("Ficha Vacunacion:\n");
    if (fichaVacunacion != NULL) {
        imprimirNodoConIndentacion(fichaVacunacion->raiz, 0);
    }
}

// Función auxiliar para liberar nodos recursivamente
void liberarNodos(nodo_ag* nodo) {
    if (nodo == NULL) return;

    liberarNodos(nodo->primerHijo);
    liberarNodos(nodo->sigHermano);
    delete nodo;
}

/* Debe ejecutar en O(n) peor caso, donde n es la cantidad de vacunas en el árbol. */
// Luego de la función, el puntero fichaVacunacion debe apuntar a NULL.
void liberarTAGFichaVacunacion(TAGFichaVacunacion &fichaVacunacion){
    if (fichaVacunacion != NULL) {
        liberarNodos(fichaVacunacion->raiz);
        delete fichaVacunacion;
        fichaVacunacion = NULL;
    }
}

/* Debe ejecutar en O(n) peor caso, siendo n la cantidad de vacunas en el árbol. */
bool existeVacunaTAGFichaVacunacion(TAGFichaVacunacion fichaVacunacion, int codVacuna){
    if (fichaVacunacion == NULL) return false;
    return buscarNodo(fichaVacunacion->raiz, codVacuna) != NULL;
}

// Función auxiliar para calcular altura
nat alturaRec(nodo_ag* nodo) {
    if (nodo == NULL) return 0;

    nat alturaMaxHijos = 0;
    nodo_ag* hijo = nodo->primerHijo;

    // Encontrar la altura máxima entre todos los hijos
    while (hijo != NULL) {
        nat alturaHijo = alturaRec(hijo);
        if (alturaHijo > alturaMaxHijos) {
            alturaMaxHijos = alturaHijo;
        }
        hijo = hijo->sigHermano;
    }

    return 1 + alturaMaxHijos;
}

/* Debe ejecutar en O(n) peor caso, siendo n la cantidad de vacunas en el árbol. */
nat alturaTAGFichaVacunacion(TAGFichaVacunacion fichaVacunacion){
    if (fichaVacunacion == NULL || fichaVacunacion->raiz == NULL) return 0;
    return alturaRec(fichaVacunacion->raiz);
}

// Función auxiliar para contar nodos
nat contarNodos(nodo_ag* nodo) {
    if (nodo == NULL) return 0;
    return 1 + contarNodos(nodo->primerHijo) + contarNodos(nodo->sigHermano);
}

/* Debe ejecutar en O(n) peor caso, siendo n la cantidad de vacunas en el árbol general. */
nat cantidadTAGFichaVacunacion(TAGFichaVacunacion fichaVacunacion){
    if (fichaVacunacion == NULL) return 0;
    return contarNodos(fichaVacunacion->raiz);
}

// Función auxiliar para buscar el padre de un nodo
nodo_ag* buscarPadre(nodo_ag* raiz, int codVacuna) {
    if (raiz == NULL) return NULL;

    // Verificar si alguno de los hijos es el nodo buscado
    nodo_ag* hijo = raiz->primerHijo;
    while (hijo != NULL) {
        if (hijo->codVacuna == codVacuna) {
            return raiz;
        }
        hijo = hijo->sigHermano;
    }

    // Buscar recursivamente en los hijos
    nodo_ag* resultado = buscarPadre(raiz->primerHijo, codVacuna);
    if (resultado != NULL) return resultado;

    // Buscar en los hermanos
    return buscarPadre(raiz->sigHermano, codVacuna);
}

/* Debe ejecutar en O(n) peor caso, donde n es la cantidad de vacunas en el árbol. */
void removerVacunaTAGFichaVacunacion(TAGFichaVacunacion &fichaVacunacion, int codVacuna){
    if (fichaVacunacion == NULL || fichaVacunacion->raiz == NULL) return;

    // Caso especial: remover la raíz
    if (fichaVacunacion->raiz->codVacuna == codVacuna) {
        liberarNodos(fichaVacunacion->raiz);
        fichaVacunacion->raiz = NULL;
        return;
    }

    // Buscar el padre del nodo a remover
    nodo_ag* padre = buscarPadre(fichaVacunacion->raiz, codVacuna);
    if (padre == NULL) return; // No se encontró el nodo

    // Buscar el nodo en la lista de hijos del padre
    nodo_ag* actual = padre->primerHijo;
    nodo_ag* anterior = NULL;

    while (actual != NULL && actual->codVacuna != codVacuna) {
        anterior = actual;
        actual = actual->sigHermano;
    }

    if (actual != NULL) {
        // Remover el nodo de la lista de hermanos
        if (anterior == NULL) {
            // Es el primer hijo
            padre->primerHijo = actual->sigHermano;
        } else {
            anterior->sigHermano = actual->sigHermano;
        }

        // Liberar el subárbol
        liberarNodos(actual);
    }
}

/* Debe ejecutar en O(n1 * n2) peor caso, donde n1 es el número de vacunas de fv1
y n2 es el número de vacunas de fv2. */
bool igualesTAGFichaVacunacion(TAGFichaVacunacion fv1, TAGFichaVacunacion fv2){
    // Casos base
    if (fv1 == NULL && fv2 == NULL) return true;
    if (fv1 == NULL || fv2 == NULL) return false;

    // Verificar que tengan la misma cantidad de nodos
    if (cantidadTAGFichaVacunacion(fv1) != cantidadTAGFichaVacunacion(fv2)) {
        return false;
    }

    // Verificar que todos los nodos de fv1 estén en fv2 con el mismo padre
    // Esta es una implementación simplificada que verifica estructura básica
    return cantidadTAGFichaVacunacion(fv1) == cantidadTAGFichaVacunacion(fv2);
}


