#include "../include/agFichaVacunacion.h"

struct nodo_ag {
    int codVacuna;
    nodo_ag* primerHijo;
    nodo_ag* sigHermano;
};

struct rep_agFichaVacunacion {
    nodo_ag* raiz;
};

// Función auxiliar para buscar un nodo con un código específico
nodo_ag* buscarNodo(nodo_ag* nodo, int codVacuna) {
    if (nodo == NULL) return NULL;

    if (nodo->codVacuna == codVacuna) return nodo;

    // Buscar en los hijos
    nodo_ag* resultado = buscarNodo(nodo->primerHijo, codVacuna);
    if (resultado != NULL) return resultado;

    // Buscar en los hermanos
    return buscarNodo(nodo->sigHermano, codVacuna);
}

// Función auxiliar para encontrar el padre de un nodo
nodo_ag* encontrarPadre(nodo_ag* raiz, int codVacuna) {
    if (raiz == NULL || raiz->primerHijo == NULL) return NULL;
    
    // Verificar si alguno de los hijos directos es el buscado
    nodo_ag* hijo = raiz->primerHijo;
    while (hijo != NULL) {
        if (hijo->codVacuna == codVacuna) return raiz;
        hijo = hijo->sigHermano;
    }
    
    // Buscar recursivamente en los hijos
    hijo = raiz->primerHijo;
    while (hijo != NULL) {
        nodo_ag* encontrado = encontrarPadre(hijo, codVacuna);
        if (encontrado != NULL) return encontrado;
        hijo = hijo->sigHermano;
    }
    
    return NULL;
}

// Declaraciones de funciones auxiliares
bool compararEstructura(nodo_ag* nodo1, nodo_ag* nodo2);

/* Debe ejecutar en O(1) peor caso. */
TAGFichaVacunacion crearTAGFichaVacunacion(){
    TAGFichaVacunacion nuevaFicha = new rep_agFichaVacunacion;
    nuevaFicha->raiz = NULL;
    return nuevaFicha;
}

/* Debe ejecutar en O(n) peor caso, siendo n la cantidad de vacunas en el árbol. */
void insertarVacunaTAGFichaVacunacion(TAGFichaVacunacion &fichaVacunacion, int codVacunaPadre, int codVacuna){
    nodo_ag* nuevoNodo = new nodo_ag;
    nuevoNodo->codVacuna = codVacuna;
    nuevoNodo->primerHijo = NULL;
    nuevoNodo->sigHermano = NULL;

    if (codVacunaPadre == -1) {
        // Insertar como raíz
        fichaVacunacion->raiz = nuevoNodo;
    } else {
        // Buscar el nodo padre
        nodo_ag* padre = buscarNodo(fichaVacunacion->raiz, codVacunaPadre);

        // Insertar como primer hijo (los hermanos se insertan en orden inverso)
        nuevoNodo->sigHermano = padre->primerHijo;
        padre->primerHijo = nuevoNodo;
    }
}

// Función auxiliar para imprimir con indentación
void imprimirNodoConIndentacion(nodo_ag* nodo, int nivel) {
    if (nodo == NULL) return;

    // Imprimir indentación (4 espacios por nivel)
    for (int i = 0; i < nivel; i++) {
        printf("    ");
    }
    printf("%d\n", nodo->codVacuna);

    // Imprimir hijos
    imprimirNodoConIndentacion(nodo->primerHijo, nivel + 1);

    // Imprimir hermanos
    imprimirNodoConIndentacion(nodo->sigHermano, nivel);
}

/*Debe ejecutar en O(n) peor caso, donde n es la cantidad de vacunas en el árbol. */
void imprimirTAGFichaVacunacion(TAGFichaVacunacion fichaVacunacion){
    printf("Ficha Vacunacion:\n");
    if (fichaVacunacion != NULL) {
        imprimirNodoConIndentacion(fichaVacunacion->raiz, 0);
    }
}

// Función auxiliar para liberar nodos recursivamente
void liberarNodos(nodo_ag* nodo) {
    if (nodo == NULL) return;

    liberarNodos(nodo->primerHijo);
    liberarNodos(nodo->sigHermano);
    delete nodo;
}

/* Debe ejecutar en O(n) peor caso, donde n es la cantidad de vacunas en el árbol. */
// Luego de la función, el puntero fichaVacunacion debe apuntar a NULL.
void liberarTAGFichaVacunacion(TAGFichaVacunacion &fichaVacunacion){
    if (fichaVacunacion != NULL) {
        liberarNodos(fichaVacunacion->raiz);
        delete fichaVacunacion;
        fichaVacunacion = NULL;
    }
}

/* Debe ejecutar en O(n) peor caso, siendo n la cantidad de vacunas en el árbol. */
bool existeVacunaTAGFichaVacunacion(TAGFichaVacunacion fichaVacunacion, int codVacuna){
    if (fichaVacunacion == NULL) return false;
    return buscarNodo(fichaVacunacion->raiz, codVacuna) != NULL;
}

// Función auxiliar para calcular altura
nat alturaRec(nodo_ag* nodo) {
    if (nodo == NULL) return 0;

    nat alturaMaxHijos = 0;
    nodo_ag* hijo = nodo->primerHijo;

    // Encontrar la altura máxima entre todos los hijos
    while (hijo != NULL) {
        nat alturaHijo = alturaRec(hijo);
        if (alturaHijo > alturaMaxHijos) {
            alturaMaxHijos = alturaHijo;
        }
        hijo = hijo->sigHermano;
    }

    return 1 + alturaMaxHijos;
}

/* Debe ejecutar en O(n) peor caso, siendo n la cantidad de vacunas en el árbol. */
nat alturaTAGFichaVacunacion(TAGFichaVacunacion fichaVacunacion){
    if (fichaVacunacion == NULL || fichaVacunacion->raiz == NULL) return 0;
    return alturaRec(fichaVacunacion->raiz);
}

// Función auxiliar para contar nodos
nat contarNodos(nodo_ag* nodo) {
    if (nodo == NULL) return 0;
    return 1 + contarNodos(nodo->primerHijo) + contarNodos(nodo->sigHermano);
}

/* Debe ejecutar en O(n) peor caso, siendo n la cantidad de vacunas en el árbol general. */
nat cantidadTAGFichaVacunacion(TAGFichaVacunacion fichaVacunacion){
    if (fichaVacunacion == NULL) return 0;
    return contarNodos(fichaVacunacion->raiz);
}

/* Debe ejecutar en O(n) peor caso, donde n es la cantidad de vacunas en el árbol. */
void removerVacunaTAGFichaVacunacion(TAGFichaVacunacion &fichaVacunacion, int codVacuna) {
    if (fichaVacunacion == NULL || fichaVacunacion->raiz == NULL) return;

    // Si es la raíz
    if (fichaVacunacion->raiz->codVacuna == codVacuna) {
        nodo_ag* temp = fichaVacunacion->raiz;
        fichaVacunacion->raiz = NULL;
        liberarNodos(temp);
        return;
    }

    // Encontrar el padre del nodo a eliminar
    nodo_ag* padre = encontrarPadre(fichaVacunacion->raiz, codVacuna);
    if (padre == NULL) return;

    // Encontrar y eliminar el nodo
    nodo_ag* anterior = NULL;
    nodo_ag* actual = padre->primerHijo;

    while (actual != NULL && actual->codVacuna != codVacuna) {
        anterior = actual;
        actual = actual->sigHermano;
    }

    if (actual != NULL) {
        // Si es el primer hijo
        if (anterior == NULL) {
            padre->primerHijo = actual->sigHermano;
        } else {
            anterior->sigHermano = actual->sigHermano;
        }
        
        // Desconectar del árbol y liberar
        actual->sigHermano = NULL;
        liberarNodos(actual);
    }
}

// Función auxiliar recursiva para comparar estructura de árboles
bool compararEstructura(nodo_ag* nodo1, nodo_ag* nodo2) {
    // Casos base
    if (nodo1 == NULL && nodo2 == NULL) return true;
    if (nodo1 == NULL || nodo2 == NULL) return false;

    // Verificar que los códigos sean iguales
    if (nodo1->codVacuna != nodo2->codVacuna) return false;

    // Verificar recursivamente hijos y hermanos
    return compararEstructura(nodo1->primerHijo, nodo2->primerHijo) &&
           compararEstructura(nodo1->sigHermano, nodo2->sigHermano);
}

/* Debe ejecutar en O(n1 * n2) peor caso, donde n1 es el número de vacunas de fv1
y n2 es el número de vacunas de fv2. */
bool igualesTAGFichaVacunacion(TAGFichaVacunacion fv1, TAGFichaVacunacion fv2) {
    // Casos base
    if (fv1 == NULL && fv2 == NULL) return true;
    if (fv1 == NULL || fv2 == NULL) return false;

    // Verificar que tengan la misma cantidad de nodos
    if (cantidadTAGFichaVacunacion(fv1) != cantidadTAGFichaVacunacion(fv2)) {
        return false;
    }

    // Para cada nodo en fv1, verificar que existe en fv2 y tiene el mismo padre
    nodo_ag* actual1 = fv1->raiz;
    nodo_ag* actual2 = fv2->raiz;

    // Verificar las raíces
    if ((actual1 == NULL) != (actual2 == NULL)) return false;
    if (actual1 != NULL && actual1->codVacuna != actual2->codVacuna) return false;

    // Para cada nodo en fv1, verificar que existe en fv2 y tiene el mismo padre
    nat cantidad = cantidadTAGFichaVacunacion(fv1);
    for (nat i = 0; i < cantidad; i++) {
        nodo_ag* nodo = buscarNodo(fv1->raiz, i);
        if (nodo != NULL) {
            // Verificar que existe en fv2
            nodo_ag* nodoFv2 = buscarNodo(fv2->raiz, i);
            if (nodoFv2 == NULL) return false;

            // Verificar que tienen el mismo padre
            nodo_ag* padre1 = encontrarPadre(fv1->raiz, nodo->codVacuna);
            nodo_ag* padre2 = encontrarPadre(fv2->raiz, nodoFv2->codVacuna);

            // Si uno tiene padre y el otro no, o si ambos tienen padre pero son diferentes
            if ((padre1 == NULL) != (padre2 == NULL)) return false;
            if (padre1 != NULL && padre2 != NULL && 
                padre1->codVacuna != padre2->codVacuna) return false;
        }
    }

    return true;
}


