
#include "../include/agFichaVacunacion.h"

struct rep_agFichaVacunacion {

};
 
/* Debe ejecutar en O(1) peor caso. */
TAGFichaVacunacion crearTAGFichaVacunacion(){
    return NULL;
}

/* Debe ejecutar en O(n) peor caso, siendo n la cantidad de vacunas en el árbol. */
void insertarVacunaTAGFichaVacunacion(TAGFichaVacunacion &fichaVacunacion, int codVacunaPadre, int codVacuna){

}

/*Debe ejecutar en O(n) peor caso, donde n es la cantidad de vacunas en el árbol. */
void imprimirTAGFichaVacunacion(TAGFichaVacunacion fichaVacunacion){

}

/* Debe ejecutar en O(n) peor caso, donde n es la cantidad de vacunas en el árbol. */
// Luego de la función, el puntero fichaVacunacion debe apuntar a NULL.
void liberarTAGFichaVacunacion(TAGFichaVacunacion &fichaVacunacion){

}

/* Debe ejecutar en O(n) peor caso, siendo n la cantidad de vacunas en el árbol. */
bool existeVacunaTAGFichaVacunacion(TAGFichaVacunacion fichaVacunacion, int codVacuna){
    return false;
}

/* Debe ejecutar en O(n) peor caso, siendo n la cantidad de vacunas en el árbol. */
nat alturaTAGFichaVacunacion(TAGFichaVacunacion fichaVacunacion){
    return 0;
}

/* Debe ejecutar en O(n) peor caso, siendo n la cantidad de vacunas en el árbol general. */
nat cantidadTAGFichaVacunacion(TAGFichaVacunacion fichaVacunacion){
    return -1;
}

/* Debe ejecutar en O(n) peor caso, donde n es la cantidad de vacunas en el árbol. */
void removerVacunaTAGFichaVacunacion(TAGFichaVacunacion &fichaVacunacion, int codVacuna){

}

/* Debe ejecutar en O(n1 * n2) peor caso, donde n1 es el número de vacunas de fv1 
y n2 es el número de vacunas de fv2. */
bool igualesTAGFichaVacunacion(TAGFichaVacunacion fv1, TAGFichaVacunacion fv2){
    return false;
}

 
 