#include "../include/aplicaciones.h"

bool mismosElementos(TPila p, TColaPerros c) {
    // Verificar si ambas estructuras tienen la misma cantidad de elementos
    if (cantidadTPila(p) != cantidadTColaPerros(c)) {
        // Vaciar ambas estructuras antes de retornar
        while (cantidadTPila(p) > 0) {
            desapilarTPila(p);
        }
        while (cantidadTColaPerros(c) > 0) {
            desencolarTColaPerros(c);
        }
        return false;
    }

    // Si ambas están vacías, son iguales
    if (cantidadTPila(p) == 0) {
        return true;
    }

    // Necesito comparar en el orden de inserción
    // Para la pila, el orden de inserción es el inverso del orden de salida
    // Para la cola, el orden de inserción es el mismo que el orden de salida

    nat cantidad = cantidadTPila(p);
    int* elementosPila = new int[cantidad];
    int* elementosCola = new int[cantidad];

    // Extraer elementos de la pila (orden inverso de inserción)
    for (int i = cantidad - 1; i >= 0; i--) {
        elementosPila[i] = cimaTPila(p);
        desapilarTPila(p);
    }

    // Extraer elementos de la cola (orden de inserción)
    for (nat i = 0; i < cantidad; i++) {
        elementosCola[i] = idTPerro(frenteTColaPerros(c));
        desencolarTColaPerros(c);
    }

    // Comparar los elementos en orden de inserción
    bool sonIguales = true;
    for (nat i = 0; i < cantidad && sonIguales; i++) {
        if (elementosPila[i] != elementosCola[i]) {
            sonIguales = false;
        }
    }

    delete[] elementosPila;
    delete[] elementosCola;

    return sonIguales;
}

TPila menoresQueElResto(TLDEPerros lista) {
    TPila resultado = crearTPila();
    nat cantidadTotal = cantidadTLDEPerros(lista);

    // Para cada posición en la lista
    for (nat i = 1; i <= cantidadTotal; i++) {
        TPerro perroActual = obtenerNesimoTLDEPerros(lista, i);
        nat vitalidadActual = vitalidadTPerro(perroActual);
        bool esMenorQueTodosLosSiguientes = true;

        // Verificar si es menor que todos los que vienen después
        for (nat j = i + 1; j <= cantidadTotal && esMenorQueTodosLosSiguientes; j++) {
            TPerro otroPerro = obtenerNesimoTLDEPerros(lista, j);
            nat otraVitalidad = vitalidadTPerro(otroPerro);
            if (vitalidadActual >= otraVitalidad) {
                esMenorQueTodosLosSiguientes = false;
            }
        }

        // Si es menor que todos los siguientes, apilarlo
        if (esMenorQueTodosLosSiguientes) {
            apilarTPila(resultado, vitalidadActual);
        }
    }

    // Vaciar la lista original
    while (cantidadTLDEPerros(lista) > 0) {
        TPerro perro = removerUltimoTLDEPerros(lista);
        liberarTPerro(perro);
    }

    return resultado;
}

bool sumaPares(nat k, TConjuntoPerros c) {
    if (esVacioTConjuntoPerros(c)) {
        return false;
    }

    int cantMax = cantMaxTConjuntoPerros(c);

    // Buscar dos elementos diferentes que sumen k
    for (int i = 0; i < cantMax; i++) {
        if (perteneceTConjuntoPerros(c, i)) {
            int complemento = k - i;
            // Verificar que el complemento sea diferente de i y esté en el conjunto
            if (complemento != i && complemento >= 0 && complemento < cantMax &&
                perteneceTConjuntoPerros(c, complemento)) {
                return true;
            }
        }
    }

    return false;
}