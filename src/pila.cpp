#include "../include/pila.h"

struct rep_pila{

};

/* Debe ejecutar en O(1) peor caso. */
TPila crearTPila(){
    return NULL;
}

/* Debe ejecutar en O(n) peor caso, donde n es la cantidad de elementos en la pila. */
// Luego de la función, el puntero pila debe apuntar a NULL.
void liberarTPila(TPila &pila){

}

/* Debe ejecutar en O(1) peor caso. */
void apilarTPila(TPila &pila, int elem){

}

/* Debe ejecutar en O(n) peor caso, donde n es la cantidad de elementos en la pila */
void imprimirTPila(TPila pila){

} 

/* Debe ejecutar en O(1) peor caso. */
nat cantidadTPila(TPila pila){
    return 0;
}

/* Debe ejecutar en O(1) peor caso. */
int cimaTPila(TPila pila) {
    return 0;
}

/* Debe ejecutar en O(1) peor caso. */
void desapilarTPila(TPila &pila){

}







 
 