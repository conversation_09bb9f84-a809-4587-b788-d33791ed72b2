#include "../include/pila.h"

struct nodo_pila {
    int elem;
    nodo_pila* sig;
};

struct rep_pila{
    nodo_pila* tope;
    nat cantidad;
};

/* Debe ejecutar en O(1) peor caso. */
TPila crearTPila(){
    TPila nuevaPila = new rep_pila;
    nuevaPila->tope = NULL;
    nuevaPila->cantidad = 0;
    return nuevaPila;
}

/* Debe ejecutar en O(n) peor caso, donde n es la cantidad de elementos en la pila. */
// Luego de la función, el puntero pila debe apuntar a NULL.
void liberarTPila(TPila &pila){
    if (pila != NULL) {
        while (pila->tope != NULL) {
            nodo_pila* temp = pila->tope;
            pila->tope = pila->tope->sig;
            delete temp;
        }
        delete pila;
        pila = NULL;
    }
}

/* Debe ejecutar en O(1) peor caso. */
void apilarTPila(TPila &pila, int elem){
    nodo_pila* nuevoNodo = new nodo_pila;
    nuevoNodo->elem = elem;
    nuevoNodo->sig = pila->tope;
    pila->tope = nuevoNodo;
    pila->cantidad++;
}

/* Debe ejecutar en O(n) peor caso, donde n es la cantidad de elementos en la pila */
void imprimirTPila(TPila pila){
    if (pila == NULL || pila->tope == NULL) {
        printf("Pila:\n");
        return;
    }

    // Crear un arreglo temporal para almacenar los elementos
    int* elementos = new int[pila->cantidad];
    nodo_pila* actual = pila->tope;

    // Llenar el arreglo desde el tope hacia abajo
    for (int i = pila->cantidad - 1; i >= 0; i--) {
        elementos[i] = actual->elem;
        actual = actual->sig;
    }

    // Imprimir en el orden correcto (primero apilado primero)
    printf("Pila:");
    for (nat i = 0; i < pila->cantidad; i++) {
        printf(" %d", elementos[i]);
    }
    printf("\n");

    delete[] elementos;
}

/* Debe ejecutar en O(1) peor caso. */
nat cantidadTPila(TPila pila){
    if (pila == NULL) return 0;
    return pila->cantidad;
}

/* Debe ejecutar en O(1) peor caso. */
int cimaTPila(TPila pila) {
    // PRE: cantidadTPila(pila) > 0
    return pila->tope->elem;
}

/* Debe ejecutar en O(1) peor caso. */
void desapilarTPila(TPila &pila){
    // PRE: cantidadTPila(pila) > 0
    nodo_pila* temp = pila->tope;
    pila->tope = pila->tope->sig;
    delete temp;
    pila->cantidad--;
}








