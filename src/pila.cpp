#include "../include/pila.h"

struct nodo_pila {
    int elem;
    nodo_pila* sig;
};

struct rep_pila{
    nodo_pila* tope;
    nat cantidad;
};

/* Debe ejecutar en O(1) peor caso. */
TPila crearTPila(){
    TPila nuevaPila = new rep_pila;
    nuevaPila->tope = NULL;
    nuevaPila->cantidad = 0;
    return nuevaPila;
}

/* Debe ejecutar en O(n) peor caso, donde n es la cantidad de elementos en la pila. */
// Luego de la función, el puntero pila debe apuntar a NULL.
void liberarTPila(TPila &pila){
    if (pila != NULL) {
        while (pila->tope != NULL) {
            nodo_pila* temp = pila->tope;
            pila->tope = pila->tope->sig;
            delete temp;
        }
        delete pila;
        pila = NULL;
    }
}

/* Debe ejecutar en O(1) peor caso. */
void apilarTPila(TPila &pila, int elem){
    nodo_pila* nuevoNodo = new nodo_pila;
    nuevoNodo->elem = elem;
    nuevoNodo->sig = pila->tope;
    pila->tope = nuevoNodo;
    pila->cantidad++;
}

/* Debe ejecutar en O(n) peor caso, donde n es la cantidad de elementos en la pila */
void imprimirTPila(TPila pila){
    printf("Pila:");
    if (pila != NULL && pila->tope != NULL) {
        // Usar una pila auxiliar para invertir el orden
        TPila pilaAux = crearTPila();

        // Pasar todos los elementos a la pila auxiliar
        while (cantidadTPila(pila) > 0) {
            int elem = cimaTPila(pila);
            desapilarTPila(pila);
            apilarTPila(pilaAux, elem);
        }

        // Imprimir y restaurar la pila original
        while (cantidadTPila(pilaAux) > 0) {
            int elem = cimaTPila(pilaAux);
            desapilarTPila(pilaAux);
            printf(" %d", elem);
            apilarTPila(pila, elem);
        }

        liberarTPila(pilaAux);
    }
    printf("\n");
}

/* Debe ejecutar en O(1) peor caso. */
nat cantidadTPila(TPila pila){
    if (pila == NULL) return 0;
    return pila->cantidad;
}

/* Debe ejecutar en O(1) peor caso. */
int cimaTPila(TPila pila) {
    // PRE: cantidadTPila(pila) > 0
    return pila->tope->elem;
}

/* Debe ejecutar en O(1) peor caso. */
void desapilarTPila(TPila &pila){
    // PRE: cantidadTPila(pila) > 0
    nodo_pila* temp = pila->tope;
    pila->tope = pila->tope->sig;
    delete temp;
    pila->cantidad--;
}








