
#include "../include/abbPersonas.h"

struct rep_abbPersonas { // Árbol Binario de Búsqueda de personas mediante su CI.
    TPersona persona;     // <PERSON><PERSON> almacenado (persona).
    TABBPersonas izq;    // Puntero al subárbol izquierdo (CIs menores)
    TABBPersonas der;    // Puntero al subárbol derecho (CIs mayores)
};

TABBPersonas crearTABBPersonasVacio(){
    return NULL;
}

void insertarTPersonaTABBPersonas(TABBPersonas &abbPersonas, TPersona persona){
    if (abbPersonas == NULL) {
        abbPersonas = new rep_abbPersonas;
        abbPersonas->persona = copiarTPersona(persona);
        abbPersonas->izq = NULL;
        abbPersonas->der = NULL;
        liberarTPersona(persona); // Libera la persona original después de copiarla
    } else {
        if (ciTPersona(persona) < ciTPersona(abbPersonas->persona)) {
            insertarTPersonaTABBPersonas(abbPersonas->izq, persona);
        } else {
            insertarTPersonaTABBPersonas(abbPersonas->der, persona);
        }
    }
}

void imprimirTABBPersonas(TABBPersonas abbPersonas){ // Recursión in-orden para imprimir el árbol.
    if (abbPersonas != NULL) {
        imprimirTABBPersonas(abbPersonas->izq);
        imprimirTPersona(abbPersonas->persona);
        imprimirTABBPersonas(abbPersonas->der);
    }
}

void liberarNodoABBPersonas(TABBPersonas &nodo) { // Función auxiliar para liberar un nodo (por referencia).
    if (nodo != NULL) {
        liberarTPersona(nodo->persona); // Libera la persona almacenada.
        delete nodo;                    // Libera el nodo.
        nodo = NULL;
    }
}

void liberarTABBPersonas(TABBPersonas &abbPersonas){ // Función para liberar postorder el árbol.
    if (abbPersonas != NULL) {
        liberarTABBPersonas(abbPersonas->izq);
        liberarTABBPersonas(abbPersonas->der);
        liberarNodoABBPersonas(abbPersonas);
    }
}

bool existeTPersonaTABBPersonas(TABBPersonas abbPersonas, int ciPersona){
    if (abbPersonas == NULL) {
        return false;
    }
    int ciActual = ciTPersona(abbPersonas->persona);
    if (ciPersona == ciActual) { // Recursión preorden.
        return true;
    } else if (ciPersona < ciActual) { 
        return existeTPersonaTABBPersonas(abbPersonas->izq, ciPersona);
    } else { 
        return existeTPersonaTABBPersonas(abbPersonas->der, ciPersona);
    }
}

TPersona obtenerTPersonaTABBPersonas(TABBPersonas abbPersonas, int ciPersona){ //Búsqueda de una persona por su CI, mediante recursión preorden.
    if (abbPersonas == NULL) {
        return NULL;
    }
    int ciActual = ciTPersona(abbPersonas->persona);
    if (ciPersona == ciActual) {
        return abbPersonas->persona;
    } else if (ciPersona < ciActual) {
        return obtenerTPersonaTABBPersonas(abbPersonas->izq, ciPersona);
    } else {
        return obtenerTPersonaTABBPersonas(abbPersonas->der, ciPersona);
    }
}

nat alturaTABBPersonas(TABBPersonas abbPersonas){
    if (abbPersonas == NULL) {
        return 0;
    } else { //Recursión postorden para calcular la altura del árbol.
        nat alturaIzq = alturaTABBPersonas(abbPersonas->izq);
        nat alturaDer = alturaTABBPersonas(abbPersonas->der);
        nat maxAlturaSubarboles;
        if (alturaIzq > alturaDer) {
            maxAlturaSubarboles = alturaIzq;
        } else {
            maxAlturaSubarboles = alturaDer;
        }
        return 1 + maxAlturaSubarboles;
    }
}

TPersona maxCITPersonaTABBPersonas(TABBPersonas abbPersonas){ 
    if (abbPersonas == NULL) {
        return NULL;
    } else if (abbPersonas->der == NULL) {
        return abbPersonas->persona;
    } else {
        return maxCITPersonaTABBPersonas(abbPersonas->der);
    }
}

TABBPersonas minimoNodoABBPersonas(TABBPersonas abbPersonas) { // Función auxiliar para encontrar el nodo con el menor CI en el árbol.
    if (abbPersonas == NULL) {
        return NULL;
    } else if (abbPersonas->izq == NULL) {
        return abbPersonas; // Se encontró y restorna el nodo más a la izquierda (menor CI).
    } else { // Si no es el menor, se aplica recursión a la izquierda.
        return minimoNodoABBPersonas(abbPersonas->izq);
    }
}

void removerTPersonaTABBPersonas(TABBPersonas &abbPersonas, int ciPersona) {
    if (abbPersonas == NULL) {
        return;
    }
    int ciActual = ciTPersona(abbPersonas->persona);
    if (ciPersona < ciActual) { // Búsqueda recursiva preorden de la CI (persona) a eliminar.
        removerTPersonaTABBPersonas(abbPersonas->izq, ciPersona);
    } else if (ciPersona > ciActual) {
        removerTPersonaTABBPersonas(abbPersonas->der, ciPersona);
    } else { // Se encontró el nodo a eliminar.
        if (abbPersonas->izq == NULL) {                 // Caso 1: Nodo hoja o con un solo hijo derecho.
            TABBPersonas nodoAEliminar = abbPersonas;   // Se guarda el nodo a eliminar.
            abbPersonas = abbPersonas->der;             // Se conecta el hijo derecho al padre.
            liberarTPersona(nodoAEliminar->persona);    // Se libera la información de la persona.
            delete nodoAEliminar;                       // Se libera el nodo.
        } else if (abbPersonas->der == NULL) {          // Caso 2: Nodo solo con hijo izquierdo.
            TABBPersonas nodoAEliminar = abbPersonas;   // Se guarda el nodo a eliminar.
            abbPersonas = abbPersonas->izq;             // Se conecta el hijo izquierdo al padre.    
            liberarTPersona(nodoAEliminar->persona);    // Se libera la información de la persona.
            delete nodoAEliminar;                       // Se libera el nodo.
        } else {                                                            // Caso 3: Nodo con ambos hijos.
            TABBPersonas sucesor = minimoNodoABBPersonas(abbPersonas->der); // Se busca el sucesor (menor en el subárbol derecho).
            int cimenor = ciTPersona(sucesor->persona);                   // Se obtiene el CI del sucesor.
            liberarTPersona(abbPersonas->persona);                          // Se libera la persona del nodo actual.
            abbPersonas->persona = copiarTPersona(sucesor->persona);        // Se copia la persona del sucesor al nodo actual. 
            removerTPersonaTABBPersonas(abbPersonas->der, cimenor);       // Se elimina mediante recursión el sucesor (originalmente en el subárbol derecho).
        }
    }
}

int cantidadTABBPersonas(TABBPersonas abbPersonas){ //Recursión postorden para contar la cantidad de personas (nodos) en el árbol.
    if (abbPersonas == NULL) {
        return 0;
    }
    return 1 + cantidadTABBPersonas(abbPersonas->izq) + cantidadTABBPersonas(abbPersonas->der);
}

TPersona obtenerNesimaPersonaTABBPersonas(TABBPersonas abbPersonas, int n){
    int cantIzq = cantidadTABBPersonas(abbPersonas->izq); // Cantidad de personas en el subárbol izquierdo.
    if (n == cantIzq + 1) { // Es true, si la raíz del árbol pasado por parámetro es la Nésima persona.
        return abbPersonas->persona;
    } else if (n <= cantIzq) { // Si la Nésima persona está en el subárbol izquierdo.
        return obtenerNesimaPersonaTABBPersonas(abbPersonas->izq, n); // Se aplica recursión al subárbol izquierdo.
    } else { // Si la Nésima persona está en el subárbol derecho.
        return obtenerNesimaPersonaTABBPersonas(abbPersonas->der, n - cantIzq - 1); // Se aplica recursión al subárbol derecho, considerando que
    }                                                                               // ya se verificaron las cantIzq personas entre el subárbol izquierdo + raíz del árbol.
}
                                                                                    
TABBPersonas filtradoPorFechaDeNacimientoTABBPersonas(TABBPersonas abbPersonas, TFecha fecha, int criterio){ // Aplicacion de resolución del ejercicio 4 del práctico Estructuras Arborescentes.
    if (abbPersonas == NULL) {
        return NULL;
    }
    TABBPersonas fizq = filtradoPorFechaDeNacimientoTABBPersonas(abbPersonas->izq, fecha, criterio);
    TABBPersonas fder = filtradoPorFechaDeNacimientoTABBPersonas(abbPersonas->der, fecha, criterio);
    int cmp = compararTFechas(fechaNacimientoTPersona(abbPersonas->persona), fecha);
    bool cumpleCriterio = (criterio < 0 && cmp < 0) ||
                          (criterio == 0 && cmp == 0) ||
                          (criterio > 0 && cmp > 0);
    if (cumpleCriterio) {
        TABBPersonas nuevoNodo = new rep_abbPersonas;
        nuevoNodo->persona = copiarTPersona(abbPersonas->persona);
        nuevoNodo->izq = fizq;
        nuevoNodo->der = fder;
        return nuevoNodo;
    } else {
        if (fizq == NULL) {
            return fder;
        } else if (fder == NULL) {
            return fizq;
        } else {
            TABBPersonas menor = minimoNodoABBPersonas(fder);
            TPersona menorPersonaCopia = copiarTPersona(menor->persona);
            int cimenor = ciTPersona(menorPersonaCopia);
            TABBPersonas ArbolFiltrado = new rep_abbPersonas;
            ArbolFiltrado->persona = menorPersonaCopia;
            ArbolFiltrado->izq = fizq;
            removerTPersonaTABBPersonas(fder, cimenor);
            ArbolFiltrado->der = fder;
            return ArbolFiltrado;
        }
    }
}