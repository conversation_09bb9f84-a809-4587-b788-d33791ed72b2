#include "../include/lseAdopciones.h"

struct rep_lseadopciones {
	TPersona persona;
	TPerro perro;
	TFecha fecha;
	rep_lseadopciones * sig;
};

TLSEAdopciones crearTLSEAdopcionesVacia(){
	TLSEAdopciones esVacia = NULL;
	return esVacia;
}

bool esVaciaTLSEAdopciones(TLSEAdopciones lseAdopciones){
	return lseAdopciones == NULL;
}

void imprimirTLSEAdopciones(TLSEAdopciones lseAdopciones){
	while (lseAdopciones != NULL){
		printf("---------------------------\n");
		printf ("Adopcion en fecha ");
		imprimirTFecha(lseAdopciones->fecha);
		printf ("Adoptante:\n");
		printf("Persona %s %s\n", nombreTPersona(lseAdopciones->persona), apellido<PERSON>ersona(lseAdopciones->persona));
		printf("CI: %d\n", ciTPersona(lseAdopciones->persona));
		printf ("Adoptado:\n");
		printf ("Perro %d\n", idTPerro(lseAdopciones->perro));
		printf ("Nombre: %s\n", nombreTPerro(lseAdopciones->perro));
		printf ("Fecha de ingreso: ");
		imprimirTFecha(fechaIngresoTPerro(lseAdopciones->perro));
		printf("---------------------------\n");
		lseAdopciones = lseAdopciones->sig;
	}
}

void liberarTLSEAdopciones(TLSEAdopciones &lseAdopciones){
	while (lseAdopciones != NULL){
		TLSEAdopciones p = lseAdopciones;
		lseAdopciones = lseAdopciones->sig;
		liberarTPersona(p->persona);
		liberarTPerro(p->perro);
		liberarTFecha(p->fecha);
		delete p;
	}
}

void insertarTLSEAdopciones(TLSEAdopciones &lseAdopciones, TFecha fecha, TPersona persona, TPerro perro){
	TLSEAdopciones q = new rep_lseadopciones;
	q->fecha = fecha;
	q->persona = persona;
	q->perro = perro;
	if (esVaciaTLSEAdopciones(lseAdopciones)) { // Si la lista es vacía, se agrega el nuevo elemento con puntero a NULL.
		q->sig = NULL;
		lseAdopciones = q;
	} else if (compararTFechas(fecha, lseAdopciones->fecha) == -1) { // Si la fecha es menor a la fecha del primer elemento de la lista, se agrega al comienzo de la lista.
		q->sig = lseAdopciones;
		lseAdopciones = q;
	} else {
		TLSEAdopciones r = lseAdopciones; // Puntero auxiliar para recorrer la lista.
		while ((r->sig != NULL) && (compararTFechas(fecha, r->sig->fecha) != -1)){
			r = r->sig;
		};
		if (r->sig == NULL){
			q->sig = NULL;
			r->sig = q;
		} else {
			q->sig = r->sig;
			r->sig = q;
		}
	}
}

bool existeAdopcionTLSEAdopciones(TLSEAdopciones lseAdopciones, int ciPersona, int idPerro){
	if (esVaciaTLSEAdopciones(lseAdopciones)){
		return false;
	} else {
		TLSEAdopciones r = lseAdopciones;
		while (r != NULL && ciTPersona(r->persona) != ciPersona){
			r = r->sig;
		}
		if (r == NULL){
			return false;
		} else {
			return idTPerro(r->perro) == idPerro;
		}
	}
}

void removerAdopcionTLSEAdopciones(TLSEAdopciones &lseAdopciones, int ciPersona, int idPerro){ // PRE: existeAdopcionTLSEAdopciones(lseAdopciones, ciPersona, idPerro) == true
	if (lseAdopciones->sig == NULL){ // Si solo tiene una adopción cargada, libero toda la lista.
		liberarTLSEAdopciones(lseAdopciones); 
	} else if (ciTPersona(lseAdopciones->persona) == ciPersona && idTPerro(lseAdopciones->perro) == idPerro){ // Si esta al comienzo de la lista, empiezo la lista desde el siguiente y libero memoria del nodo "suelto".
		TLSEAdopciones primero = lseAdopciones;
		lseAdopciones = lseAdopciones->sig;
		liberarTFecha(primero->fecha);
		liberarTPerro(primero->perro);
		liberarTPersona(primero->persona);
		delete primero;
	} else {
		TLSEAdopciones r = lseAdopciones;
		while (r->sig != NULL && (ciTPersona(r->sig->persona) != ciPersona || idTPerro(r->sig->perro) != idPerro)){ // Si la persona no esta al comienzo de la lista, los buscamos y luego enlazamos el nodo anterior con el nodo posterior al que eliminaremos. 
			r = r->sig;
		}
		if (r->sig == NULL){ // Si es el último nodo con datos de la lista, pasa a ser NULL.
			liberarTFecha(r->fecha);
			liberarTPerro(r->perro);
			liberarTPersona(r->persona);
			r = NULL;
		} else { // Si el nodo que estamos buscando esta en el medio de la lista, se enlazan los nodos que son consecutivos con el que vamos a eliminar.
			TLSEAdopciones nodo_borrar = r->sig;
			r->sig = nodo_borrar->sig;
			liberarTFecha(nodo_borrar->fecha);
			liberarTPerro(nodo_borrar->perro);
			liberarTPersona(nodo_borrar->persona);
			delete nodo_borrar;
		}
	}
}