#include "../include/colaPerros.h"

struct nodo_cola {
    TPerro perro;
    nodo_cola* sig;
};

struct rep_colaPerros {
    nodo_cola* frente;
    nodo_cola* final;
    nat cantidad;
};

/* Debe ejecutar en O(1) peor caso. */
TColaPerros crearTColaPerros(){
    TColaPerros nuevaCola = new rep_colaPerros;
    nuevaCola->frente = NULL;
    nuevaCola->final = NULL;
    nuevaCola->cantidad = 0;
    return nuevaCola;
}

/* Debe ejecutar en O(n) peor caso, donde n es la cantidad de perros
en la cola.*/
// Debe liberar toda la memoria asociada a la cola, incluidos sus perros.
// Luego de la función, el puntero colaPerros debe apuntar a NULL.
void liberarTColaPerros(TColaPerros &colaPerros){
    if (colaPerros != NULL) {
        while (colaPerros->frente != NULL) {
            nodo_cola* temp = colaPerros->frente;
            colaPerros->frente = colaPerros->frente->sig;
            liberarTPerro(temp->perro);
            delete temp;
        }
        delete colaPerros;
        colaPerros = NULL;
    }
}

/* Debe ejecutar en O(1) peor caso. */
// El resultado comparte memoria con el perro pasado por parámetro.
void encolarTColaPerros(TColaPerros &colaPerros, TPerro perro){
    nodo_cola* nuevoNodo = new nodo_cola;
    nuevoNodo->perro = perro;
    nuevoNodo->sig = NULL;

    if (colaPerros->cantidad == 0) {
        // Cola vacía
        colaPerros->frente = nuevoNodo;
        colaPerros->final = nuevoNodo;
    } else {
        // Cola no vacía
        colaPerros->final->sig = nuevoNodo;
        colaPerros->final = nuevoNodo;
    }
    colaPerros->cantidad++;
}

/* Debe ejecutar en O(n) peor caso, donde n es el número de perros en la cola */
void imprimirTColaPerros(TColaPerros colaPerros){
    printf("Cola de Perros:\n");
    if (colaPerros != NULL) {
        nodo_cola* actual = colaPerros->frente;
        while (actual != NULL) {
            imprimirTPerro(actual->perro);
            actual = actual->sig;
        }
    }
}

/* Debe ejecutar en O(1) peor caso. */
nat cantidadTColaPerros(TColaPerros colaPerros){
    if (colaPerros == NULL) return 0;
    return colaPerros->cantidad;
}

/* Debe ejecutar en O(1) peor caso. */
TPerro frenteTColaPerros(TColaPerros colaPerros) {
    // PRE: cantidadTColaPerros(colaPerros) > 0
    return colaPerros->frente->perro;
}

/* Debe ejecutar en O(1) peor caso. */
void desencolarTColaPerros(TColaPerros &colaPerros) {
    // PRE: cantidadTColaPerros(colaPerros) > 0
    nodo_cola* temp = colaPerros->frente;
    colaPerros->frente = colaPerros->frente->sig;

    if (colaPerros->frente == NULL) {
        // La cola quedó vacía
        colaPerros->final = NULL;
    }

    // Liberar el perro porque no hay forma de que el llamador lo recupere
    liberarTPerro(temp->perro);
    delete temp;
    colaPerros->cantidad--;
}
