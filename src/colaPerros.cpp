#include "../include/colaPerros.h"

struct rep_cola<PERSON>er<PERSON> {
    
};

/* Debe ejecutar en O(1) peor caso. */
TColaPerros crearTColaPerros(){
    return NULL;
}

/* Debe ejecutar en O(n) peor caso, donde n es la cantidad de perros 
en la cola.*/
// Debe liberar toda la memoria asociada a la cola, incluidos sus perros.
// Luego de la función, el puntero colaPerros debe apuntar a NULL.
void liberarTColaPerros(TColaPerros &colaPerros){
    
}

/* Debe ejecutar en O(1) peor caso. */
// El resultado comparte memoria con el perro pasado por parámetro. 
void encolarTColaPerros(TColaPerros &colaPerros, TPerro perro){

}

/* Debe ejecutar en O(n) peor caso, donde n es el número de perros en la cola */
void imprimirTColaPerros(TColaPerros colaPerros){

}

/* Debe ejecutar en O(1) peor caso. */
nat cantidadTColaPerros(TColaPerros colaPerros){
    return 0;
}

/* Debe ejecutar en O(1) peor caso. */
TPerro frenteTColaPerros(TColaPerros colaPerros) {
    return NULL;
}

/* Debe ejecutar en O(1) peor caso. */
void desencolarTColaPerros(TColaPerros &colaPerros) {

}
