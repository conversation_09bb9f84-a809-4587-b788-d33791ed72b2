#include "../include/perro.h"

struct rep_perro {
    /************ Parte 4.1 ************/
    /*Escriba el código a continuación */
    int id;
    char nombre[MAX_NOMBRE];
    nat edad;
    nat vitalidad;
    char descripcion[MAX_DESCRIPCION];
    TFecha fechaIngreso;
    /****** Fin de parte Parte 4.1 *****/
};

TPerro crearTPerro(int id, const char nombre[MAX_NOMBRE], nat edad, nat vitalidad, const char descripcion[MAX_DESCRIPCION], TFecha fechaIngreso) {
    TPerro nuevoPerro = new rep_perro;
        nuevoPerro->id = id;
        strcpy(nuevoPerro->nombre, nombre);
        nuevoPerro->edad = edad;
        nuevoPerro->vitalidad = vitalidad;
        strcpy(nuevoPerro->descripcion, descripcion);
        nuevoPerro->fechaIngreso = fechaIngreso;
        return nuevoPerro; 
}

void liberarTPerro(TPerro &perro) {
    liberarTFecha(perro->fechaIngreso);
    delete perro;
    perro = NULL;
}

int idTPerro(TPerro perro) {
    int id = perro->id;
    return id;
}

char* nombreTPerro(TPerro perro) {
    char* nombre = perro->nombre;
    return nombre;
}

nat edadTPerro(TPerro perro) {
    nat edad = perro->edad;
    return edad;
}

nat vitalidadTPerro(TPerro perro) {
    nat vitalidad = perro->vitalidad;
    return vitalidad;
}

char* descripcionTPerro(TPerro perro) {
    char* descripcion = perro->descripcion;
    return descripcion;
}

TFecha fechaIngresoTPerro(TPerro perro) {
    TFecha fechaIngreso = perro->fechaIngreso;
    return fechaIngreso;
}

void imprimirTPerro(TPerro perro) {
    printf("Perro %d\n", perro->id);
    printf("Nombre: %s\n", perro->nombre);
    printf("Edad: %d\n", perro->edad);
    printf("Descripcion: %s\n", perro->descripcion);
    printf("Fecha de ingreso: ");
    imprimirTFecha(perro->fechaIngreso);
    printf("Vitalidad: %d\n", perro->vitalidad);
}

TPerro copiarTPerro(TPerro perro) {
    TPerro copiaPerro = new rep_perro;
    copiaPerro->id = perro->id;
    strcpy(copiaPerro->nombre, perro->nombre);
    copiaPerro->edad = perro->edad;
    copiaPerro->vitalidad = perro->vitalidad;
    strcpy(copiaPerro->descripcion, perro->descripcion);
    copiaPerro->fechaIngreso = copiarTFecha(perro->fechaIngreso);
    return copiaPerro;
}

void actualizarEdadTPerro(TPerro &perro, nat nuevaEdad) {
    perro->edad = nuevaEdad;
}

void actualizarVitalidadTPerro(TPerro &perro, nat nuevaVitalidad) {
    perro->vitalidad = nuevaVitalidad;
}