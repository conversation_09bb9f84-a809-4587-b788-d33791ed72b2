#include "../include/persona.h"

struct rep_persona {
    int ci;
    char nombre[MAX_NOMBRE_PERSONA];
    char apellido[MAX_APELLIDO_PERSONA];
    TFecha fechaNacPersona;
    <PERSON><PERSON><PERSON><PERSON><PERSON>[MAX_PERROS_PERSONA]; // Arreglo con máximo, para almacenar los perros adoptados por la persona.
    int cantidadPerros; // Tope para el arreglo de perros adoptados.
};

TPersona crearTPersona(int ci, const char nombre[MAX_NOMBRE_PERSONA], const char apellido[MAX_APELLIDO_PERSONA], nat diaNac, nat mesNac, nat anioNac){
    TPersona nuevaPersona = new rep_persona;
        nuevaPersona->ci = ci;
        strcpy(nuevaPersona->nombre, nombre);
        strcpy(nuevaPersona->apellido, apellido);
        nuevaPersona->fechaNacPersona = crearTFecha(diaNac, mesNac, anioNac);
        nuevaPersona->cantidadPerros = 0; // Inicializar cantidad de perros adoptados.
        return nuevaPersona; 
}

void imprimirTPersona(TPersona persona){
    printf("Persona %s %s\n", persona->nombre, persona->apellido);
    printf("CI: %d\n", persona->ci);
    printf("Fecha de Nacimiento: ");
    imprimirTFecha(persona->fechaNacPersona);
    printf("Perros adoptados:\n");
    for (int j = 0; j < persona->cantidadPerros; j++) { // El bucle for tiene un máximo de MAX_PERROS_PERSONA iteraciones, lo que lo hace O(1)
        imprimirTPerro(persona->perros[j]);
    } 
}

void liberarTPersona(TPersona &persona){
    liberarTFecha(persona->fechaNacPersona);
    if (persona->cantidadPerros > 0) { // En el caso que la paersona haya adoptado perros, estos se liberan.
        for (int j = persona->cantidadPerros - 1; j >= 0; j--) { // Liberar los perros adoptados (máximo de MAX_PERROS_PERSONA iteraciones, lo que lo hace O(1))
            liberarTPerro(persona->perros[j]);
        }
    }
    delete persona;
    persona = NULL;
}

int ciTPersona(TPersona persona){
    return persona->ci;
}

char* nombreTPersona(TPersona persona){
    return persona->nombre;
}

char* apellidoTPersona(TPersona persona){
    return persona->apellido;
}

TFecha fechaNacimientoTPersona(TPersona persona){
    return persona->fechaNacPersona;
}

void agregarPerroTPersona(TPersona &persona, TPerro perro){
    if (persona->cantidadPerros < MAX_PERROS_PERSONA){
        persona->perros[persona->cantidadPerros] = copiarTPerro(perro);
        persona->cantidadPerros ++;
    }
}

bool pertenecePerroTPersona(TPersona persona, int idPerro){
    int i = 0;
    while (i < persona->cantidadPerros && idTPerro(persona->perros[i]) != idPerro){
        i++;
    }
    return i < persona->cantidadPerros;
}

int cantidadPerrosTPersona(TPersona persona){
    return persona->cantidadPerros; 
}

TPersona copiarTPersona(TPersona persona){
    TPersona copiaPersona = new rep_persona;
    copiaPersona->ci = persona->ci;
    strcpy(copiaPersona->nombre, persona->nombre);
    strcpy(copiaPersona->apellido, persona->apellido);
    copiaPersona->fechaNacPersona = copiarTFecha(persona->fechaNacPersona);
    copiaPersona->cantidadPerros = persona->cantidadPerros;
    if (persona->cantidadPerros > 0){
        for (int j = 0; j < persona->cantidadPerros; j++){
            copiaPersona->perros[j] = copiarTPerro(persona->perros[j]);
        }
    }
    return copiaPersona;
}