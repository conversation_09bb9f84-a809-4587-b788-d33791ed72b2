1> Se creó conjunto perros con cantMax 3 exitosamente.
2> Se ejecutó 'insertarTConjuntoPerros con perro de id 2 exitosamente.
3> El perro de id 2 pertenece al conjunto.
4> Se ejecutó 'insertarTConjuntoPerros con perro de id 0 exitosamente.
5> Se ejecutó 'insertarTConjuntoPerros con perro de id 1 exitosamente.
6> El perro de id 0 pertenece al conjunto.
7> El perro de id 1 pertenece al conjunto.
8> El perro de id 2 pertenece al conjunto.
9> El perro de id 3 NO pertenece al conjunto.
10> El perro de id 4 NO pertenece al conjunto.
11> Conjunto de perros:
0 1 2
12> Se ejecutó 'borrarDeTConjuntoPerros con perro de id 0 exitosamente.
13> Se ejecutó 'borrarDeTConjuntoPerros con perro de id 1 exitosamente.
14> Se ejecutó 'borrarDeTConjuntoPerros con perro de id 2 exitosamente.
15> Conjunto de perros:

16> El cardinal del conjunto es: 0.
17> La cantidad máxima del conjunto es: 3.
18> Se ejecutó 'insertarTConjuntoPerros con perro de id 0 exitosamente.
19> Se ejecutó 'borrarDeTConjuntoPerros con perro de id 2 exitosamente.
20> Se ejecutó 'insertarTConjuntoPerros con perro de id 1 exitosamente.
21> La conjunto de perros NO es vacío.
22> El cardinal del conjunto es: 2.
23> La cantidad máxima del conjunto es: 3.
24> Se ejecutó 'borrarDeTConjuntoPerros con perro de id 1 exitosamente.
25> Conjunto de perros:
0
26> Se ejecutó 'liberarTConjuntoPerros' exitosamente.
27> Fin.
