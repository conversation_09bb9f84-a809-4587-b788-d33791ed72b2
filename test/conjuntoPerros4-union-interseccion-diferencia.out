1> Se creó conjunto perros con cantMax 10 exitosamente.
2> Se ejecutó 'insertarTConjuntoPerros con perro de id 0 exitosamente.
3> Se ejecutó 'insertarTConjuntoPerros con perro de id 1 exitosamente.
4> Se ejecutó 'insertarTConjuntoPerros con perro de id 2 exitosamente.
5> Se ejecutó 'insertarTConjuntoPerros con perro de id 3 exitosamente.
6> Se ejecutó 'insertarTConjuntoPerros con perro de id 6 exitosamente.
7> Se ejecutó 'insertarTConjuntoPerros con perro de id 7 exitosamente.
8> Se ejecutó 'insertarTConjuntoPerros con perro de id 8 exitosamente.
9> Se ejecutó 'insertarTConjuntoPerros con perro de id 9 exitosamente.
10> Conjunto de perros:
0 1 2 3 6 7 8 9
11> #  unión con 1.
12> Conjunto de perros unión:
0 1 2 3 6 7 8 9
13> #  intersección con 1.
14> Conjunto de perros intersección:
1
15> #  diferencia con 1.
16> Conjunto de perros diferencia:
0 2 3 6 7 8 9
17> #  intersección con 1 3 4 5 6.
18> Conjunto de perros intersección:
1 3 6
19> #  diferencia con 1 3 4 5 6.
20> Conjunto de perros diferencia:
0 2 7 8 9
21> #  unión con conjunto vacío.
22> Conjunto de perros unión:
0 1 2 3 6 7 8 9
23> #  intersección con conjunto vacío.
24> Conjunto de perros intersección:

25> #  diferencia con conjunto vacío.
26> Conjunto de perros diferencia:
0 1 2 3 6 7 8 9
27> Se ejecutó 'liberarTConjuntoPerros' exitosamente.
28> Fin.
